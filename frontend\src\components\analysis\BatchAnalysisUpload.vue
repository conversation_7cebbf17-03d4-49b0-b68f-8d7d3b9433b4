<template>
  <div>
    <!-- 文件上传区域 -->
    <div 
      @drop="handleFileDrop"
      @dragover.prevent
      @dragenter.prevent="isDragging = true"
      @dragleave.prevent="isDragging = false"
      :class="[
        'border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 cursor-pointer',
        isDragging 
          ? 'border-blue-500 bg-blue-50' 
          : 'border-gray-300 hover:border-gray-400'
      ]"
      @click="$refs.fileInput.click()"
    >
      <!-- 上传图标 -->
      <div class="flex justify-center mb-4">
        <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
          </svg>
        </div>
      </div>

      <!-- 上传文本 -->
      <h3 class="text-lg font-medium text-gray-900 mb-2">批量上传合同文件</h3>
      <p class="text-sm text-gray-500 mb-4">
        拖拽文件到此处或点击上传，支持PDF、DOCX格式，最大20MB
      </p>
      <!-- <p class="text-xs text-gray-400 mb-4">
        支持PDF、DOCX格式，最大文件大小20MB
      </p> -->
      
      <button 
        type="button"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        选择文件
      </button>

      <!-- 隐藏的文件输入 -->
      <input 
        type="file" 
        ref="fileInput" 
        class="hidden" 
        accept="application/pdf,.docx,application/vnd.openxmlformats-officedocument.wordprocessingml.document" 
        multiple
        @change="handleFileSelect" 
      />
    </div>

    <!-- 已选择文件列表 -->
    <div v-if="selectedFiles.length > 0" class="mt-6">
      <h4 class="text-sm font-medium text-gray-900 mb-3">已选择的文件 ({{ selectedFiles.length }})</h4>
      <div class="space-y-2">
        <div 
          v-for="(file, index) in selectedFiles" 
          :key="index"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"/>
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">{{ file.name }}</p>
              <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
            </div>
          </div>
          <button 
            @click="removeFile(index)"
            class="text-red-500 hover:text-red-700"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- 批量操作按钮 -->
      <div class="mt-4 flex justify-end space-x-3">
        <button 
          @click="clearAllFiles"
          type="button"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          清空所有
        </button>
        <button 
          type="button"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          disabled
        >
          开始批量分析
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const isDragging = ref(false)
const selectedFiles = ref([])
const fileInput = ref(null)

// 文件验证
const validateFile = (file) => {
  if (!file) {
    return false
  }

  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
  
  if (!allowedTypes.includes(file.type)) {
    alert('请选择PDF或DOCX文件')
    return false
  }

  // 检查文件大小
  if (file.size > 50 * 1024 * 1024) { // 50MB
    alert('文件大小不能超过50MB')
    return false
  }

  return true
}

// 文件选择处理
const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => {
    if (validateFile(file)) {
      selectedFiles.value.push(file)
    }
  })
}

// 文件拖拽处理
const handleFileDrop = (event) => {
  event.preventDefault()
  isDragging.value = false
  
  const files = Array.from(event.dataTransfer.files)
  files.forEach(file => {
    if (validateFile(file)) {
      selectedFiles.value.push(file)
    }
  })
}

// 移除单个文件
const removeFile = (index) => {
  selectedFiles.value.splice(index, 1)
}

// 清空所有文件
const clearAllFiles = () => {
  selectedFiles.value = []
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>
