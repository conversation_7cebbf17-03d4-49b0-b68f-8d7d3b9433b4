<template>
  <div class="min-h-screen w-full bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col items-center justify-center px-4">
    <!-- 主标题 -->
    <div class="text-center mb-12">
      <h1 class="text-5xl font-bold text-gray-800 mb-6">
        AI 合同风险分析系统
      </h1>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
        基于先进的人工智能技术，为您的合同提供专业的风险识别、条款解读和优化建议
      </p>
    </div>

    <!-- 分析类型选择选项卡 -->
    <div class="flex mb-8">
      <!-- 单合同分析选项卡 -->
      <button
        @click="activeTab = 'single'"
        :class="[
          'px-6 py-3 font-medium text-lg transition-all duration-300 flex items-center space-x-2',
          activeTab === 'single'
            ? 'bg-gray-900 text-white'
            : 'bg-white text-gray-700 hover:bg-gray-50'
        ]"
        class="rounded-l-lg border-r border-gray-200"
      >
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"/>
        </svg>
        <span>单合同分析</span>
      </button>

      <!-- 批量分析选项卡 -->
      <button
        @click="activeTab = 'batch'"
        :class="[
          'px-6 py-3 font-medium text-lg transition-all duration-300 flex items-center space-x-2',
          activeTab === 'batch'
            ? 'bg-gray-900 text-white'
            : 'bg-white text-gray-700 hover:bg-gray-50'
        ]"
        class="rounded-r-lg"
      >
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
        </svg>
        <span>批量分析</span>
      </button>
    </div>

    <!-- 内容区域 -->
    <div class="w-full max-w-2xl">
      <!-- 单合同分析内容 -->
      <div v-if="activeTab === 'single'" class="bg-white rounded-2xl shadow-lg p-8">
        <!-- 项目名称 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">项目名称</label>
          <input
            v-model="projectName"
            type="text"
            placeholder="例如：2024年度采购合同第3号"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
          />
        </div>

        <!-- 文件上传区域 -->
        <SingleContractUpload
          v-if="!isAnalyzing && !analysisResult"
          @file-selected="handleFileSelected"
          @start-analysis="handleStartAnalysis"
        />

        <!-- 分析进度 -->
        <div v-if="isAnalyzing" class="text-center py-8">
          <div class="inline-flex items-center px-4 py-2 bg-blue-50 rounded-lg">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-blue-700">正在分析合同，请稍候...</span>
          </div>
          <div class="mt-4 w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="`width: ${analysisProgress}%`"></div>
          </div>
          <p class="mt-2 text-sm text-gray-600">{{ progressMessage }}</p>
        </div>

        <!-- 分析完成 -->
        <div v-if="analysisResult" class="text-center py-8">
          <div class="inline-flex items-center px-4 py-2 bg-green-50 rounded-lg mb-4">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <span class="text-green-700">分析完成！</span>
          </div>
          <button
            @click="viewResults"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            查看分析结果
          </button>
          <button
            @click="resetAnalysis"
            class="ml-3 inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            重新分析
          </button>
        </div>
      </div>

      <!-- 批量分析内容 -->
      <div v-if="activeTab === 'batch'" class="bg-white rounded-2xl shadow-lg p-8">
        <BatchAnalysisUpload />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import SingleContractUpload from './analysis/SingleContractUpload.vue'
import BatchAnalysisUpload from './analysis/BatchAnalysisUpload.vue'
import { useAnalysis } from '../composables/useAnalysis'

const router = useRouter()

// 使用分析功能
const {
  handleFileSelected: setSelectedFile,
  startAnalysis,
  resetAnalysis: resetAnalysisState,
  isAnalyzing,
  analysisResult,
  analysisProgress,
  progressMessage
} = useAnalysis()

// 响应式数据
const activeTab = ref('single')
const projectName = ref('')
const selectedFile = ref(null)

// 文件选择处理
const handleFileSelected = (file) => {
  console.log('文件已选择:', file)
  selectedFile.value = file
  setSelectedFile(file)
}

// 开始分析处理
const handleStartAnalysis = async () => {
  if (!selectedFile.value) {
    alert('请先选择文件')
    return
  }

  console.log('开始分析，项目名称:', projectName.value)

  // 直接在当前页面开始分析
  startAnalysis()
}

// 查看结果
const viewResults = () => {
  if (analysisResult.value?.id) {
    router.push(`/analysis/${analysisResult.value.id}`)
  }
}

// 重置分析
const resetAnalysis = () => {
  resetAnalysisState()
  selectedFile.value = null
}
</script>