<template>
  <div class="h-screen w-full bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col items-center justify-center px-4 overflow-hidden">
    <!-- 主标题 -->
    <div class="text-center mb-16">
      <h1 class="text-5xl font-bold text-gray-800 mb-6">
        AI 合同风险分析系统
      </h1>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
        基于先进的人工智能技术，为您的合同提供专业的风险识别、条款解读和优化建议
      </p>
    </div>

    <!-- 分析类型选择 -->
    <div class="flex flex-col sm:flex-row gap-6 mb-16">
      <!-- 单合同分析按钮 -->
      <button
        @click="navigateToSingleAnalysis"
        class="group bg-gray-900 hover:bg-gray-800 text-white px-8 py-4 rounded-xl font-medium text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl min-w-[200px]"
      >
        <div class="flex items-center justify-center space-x-3">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"/>
          </svg>
          <span>单合同分析</span>
        </div>
      </button>

      <!-- 批量分析按钮 -->
      <button
        @click="navigateToBatchAnalysis"
        class="group bg-white hover:bg-gray-50 text-gray-900 border-2 border-gray-200 hover:border-gray-300 px-8 py-4 rounded-xl font-medium text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl min-w-[200px]"
      >
        <div class="flex items-center justify-center space-x-3">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
          </svg>
          <span>批量分析</span>
        </div>
      </button>
    </div>

    <!-- 特性说明 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl w-full">
      <div class="text-center p-6 bg-white rounded-xl shadow-md">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">智能识别</h3>
        <p class="text-gray-600 text-sm">
          自动识别合同中的关键条款和潜在风险点
        </p>
      </div>

      <div class="text-center p-6 bg-white rounded-xl shadow-md">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">专业分析</h3>
        <p class="text-gray-600 text-sm">
          提供专业的法律条款解读和风险评估
        </p>
      </div>

      <div class="text-center p-6 bg-white rounded-xl shadow-md">
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">优化建议</h3>
        <p class="text-gray-600 text-sm">
          根据分析结果提供合同优化和改进建议
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 导航到单合同分析
const navigateToSingleAnalysis = () => {
  router.push('/single-analysis')
}

// 导航到批量分析
const navigateToBatchAnalysis = () => {
  router.push('/batch-analysis')
}
</script> 