import { ref, onUnmounted } from 'vue'
import { useProgress } from './useProgress'
import { uploadFile, pollAnalysisResult, getAnalysisResult } from '../api/analysis'

// API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://localhost:8000"

// 全局状态 - 在组件间共享
const globalSelectedFile = ref(null)
const globalAnalysisResult = ref(null)
const globalCurrentTaskId = ref(null)
const globalPollingInterval = ref(null)

export function useAnalysis() {
  // 使用进度管理
  const {
    progress: analysisProgress,
    message: progressMessage,
    isProcessing: isAnalyzing,
    updateProgress,
    updateMessage,
    startProcessing,
    completeProcessing,
    resetProgress
  } = useProgress()

  // 使用全局状态
  const selectedFile = globalSelectedFile
  const analysisResult = globalAnalysisResult
  const currentTaskId = globalCurrentTaskId
  const pollingInterval = globalPollingInterval

  // 文件选择处理
  const handleFileSelected = (file) => {
    selectedFile.value = file
  }

  // 开始分析
  const startAnalysis = async () => {
    if (!selectedFile.value) return

    startProcessing()

    try {
      // 上传文件并获取任务ID
      const taskId = await uploadFile(selectedFile.value)
      currentTaskId.value = taskId
      updateProgress(15)
      updateMessage('uploaded')

      // 开始轮询结果
      pollingInterval.value = pollAnalysisResult(
        taskId,
        (progress) => updateProgress(progress),
        (result) => {
          analysisResult.value = result
          completeProcessing()
        },
        (error) => {
          alert("分析失败: " + error)
          isAnalyzing.value = false
        }
      )
    } catch (error) {
      console.error("分析请求失败:", error)
      alert("分析请求失败，请重试")
      isAnalyzing.value = false
    }
  }

  // 加载分析结果
  const loadAnalysisResult = async (analysisId) => {
    try {
      const result = await getAnalysisResult(analysisId)
      analysisResult.value = result
      completeProcessing()
    } catch (error) {
      console.error("加载分析结果失败:", error)
      alert("加载分析结果失败，请重试")
      isAnalyzing.value = false
    }
  }

  // 重置分析
  const resetAnalysis = () => {
    selectedFile.value = null
    analysisResult.value = null
    currentTaskId.value = null
    resetProgress()

    if (pollingInterval.value) {
      pollingInterval.value()
      pollingInterval.value = null
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    if (pollingInterval.value) {
      pollingInterval.value()
    }
  })

  return {
    // 状态
    selectedFile,
    isAnalyzing,
    analysisResult,
    analysisProgress,
    progressMessage,
    currentTaskId,
    
    // 方法
    handleFileSelected,
    startAnalysis,
    resetAnalysis,
    loadAnalysisResult
  }
} 